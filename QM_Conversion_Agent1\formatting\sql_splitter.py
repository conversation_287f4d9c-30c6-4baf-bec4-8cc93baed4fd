import re
from typing import List


def split_sql_statements(sql_code: str) -> List[str]:
    """
    Split SQL code into individual statements while preserving logical constructs.

    Universal Rules:
    1. Split on first occurrence of 'AS' or 'IS' keywords (using word boundaries)
    2. Split on semicolons that are not within comments or quotes
    3. Preserve multi-line logical constructs as single statements
    4. Do not split within SQL comments (/* */ or --)
    5. Do not split if semicolon is within single quotes
    6. Preserve case of the original SQL
    7. Maintain structural integrity of code blocks

    Args:
        sql_code (str): The SQL code to split

    Returns:
        List[str]: List of individual SQL statements with preserved structure
    """
    if not sql_code or not sql_code.strip():
        return []

    statements = []

    # Function to check if position is within a comment
    def is_in_comment(pos: int, text: str) -> bool:
        if pos >= len(text):
            return False

        # More efficient implementation for checking if position is within a comment
        # Check for block comments /* */
        block_comment_pairs = []
        block_starts = [m.start() for m in re.finditer(r'/\*', text[:pos+1])]
        if block_starts:
            # Only find ends that could potentially contain our position
            block_ends = [m.start() + 2 for m in re.finditer(r'\*/', text)]

            # Pair up start and end positions
            for start in block_starts:
                # Find the first end that comes after this start
                for end in block_ends:
                    if end > start:
                        block_comment_pairs.append((start, end))
                        break

        # Check if position is within any block comment
        for start, end in block_comment_pairs:
            if start < pos < end:
                return True

        # Check for line comments --
        line_starts = [m.start() for m in re.finditer(r'--', text[:pos+1])]
        for start in line_starts:
            line_end = text.find('\n', start)
            if line_end == -1:  # Comment goes to end of string
                line_end = len(text)
            if start < pos < line_end:
                return True

        return False

    # Function to check if position is within quotes
    def is_in_quotes(pos: int, text: str) -> bool:
        # More efficient implementation using regex to find all single quotes
        # and then determining if the position is within quotes
        if pos >= len(text):
            return False

        # Find all single quotes before the position
        quote_positions = [m.start() for m in re.finditer(r"'", text[:pos])]

        # Handle escaped quotes (two consecutive single quotes)
        # by removing pairs of consecutive quotes
        i = 0
        while i < len(quote_positions) - 1:
            if quote_positions[i+1] - quote_positions[i] == 1:  # Consecutive quotes
                quote_positions.pop(i)
                quote_positions.pop(i)
                # Don't increment i since we removed two elements
            else:
                i += 1

        # If there's an odd number of quotes, we're inside quotes
        return len(quote_positions) % 2 == 1



    # Process the SQL code
    remaining_code = sql_code.strip()

    # First, try to split on AS or IS keywords with word boundaries
    as_is_pattern = r'\b(AS|IS)\b'

    # Process the first occurrence of AS or IS
    # Find the first AS or IS with word boundaries
    match = re.search(as_is_pattern, remaining_code, re.IGNORECASE)

    if match and not is_in_comment(match.start(), remaining_code) and not is_in_quotes(match.start(), remaining_code):
        # Split before the match (don't include AS or IS)
        first_part = remaining_code[:match.start()]
        statements.append(first_part.strip())
        # Keep the AS or IS with the second part
        remaining_code = remaining_code[match.start():].strip()

    # If we still have code to process, split by semicolons only
    if remaining_code:
        # Find all semicolons
        semicolon_positions = [m.start() for m in re.finditer(';', remaining_code)]

        if not semicolon_positions:
            # No semicolons found, add the remaining code as a single statement
            statements.append(remaining_code.strip())
        else:
            last_pos = 0
            for pos in semicolon_positions:
                # Only split if semicolon is not in a comment or quotes
                if not is_in_comment(pos, remaining_code) and not is_in_quotes(pos, remaining_code):
                    statements.append(remaining_code[last_pos:pos+1].strip())
                    last_pos = pos + 1

            # Add any remaining code after the last semicolon
            if last_pos < len(remaining_code):
                statements.append(remaining_code[last_pos:].strip())

    # Filter out empty statements
    return [stmt for stmt in statements if stmt.strip()]
