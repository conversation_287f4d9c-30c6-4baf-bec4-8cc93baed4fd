from pydantic import BaseModel, Field
from typing import Optional, List, Literal


# Structured Output Models for AI Responses
class CorrectedStatement(BaseModel):
    """Model for a corrected statement."""
    statement_number: int = Field(description="The statement number")
    original_statement: str = Field(description="The original target statement")
    corrected_statement: str = Field(description="The corrected target statement")
    statement_type: str = Field(description="Type: before_error, error_statement, or after_error")
    changes_made: str = Field(description="Description of changes made")


class StatementConversionOutput(BaseModel):
    """Model for statement conversion AI output."""
    corrected_statements: List[CorrectedStatement] = Field(description="List of corrected statements")
    explanation: str = Field(description="Concise summary of the error and fix applied")


class Phase1ErrorIdentificationOutput(BaseModel):
    """Model for Phase 1 error statement identification."""
    error_statement_number: int = Field(description="The identified error statement number")
    confidence_score: float = Field(description="Confidence score between 0.0 and 1.0")
    reasoning: str = Field(description="Detailed reasoning for the identification")


class TargetStatementItem(BaseModel):
    """Model for a target statement context item."""
    target_statement_number: int = Field(description="Target statement number")
    statement_type: str = Field(description="Type: before_error, error_statement, or after_error")


class Phase2ErrorContextOutput(BaseModel):
    """Model for Phase 2 error context creation output."""
    target_statements: List[TargetStatementItem] = Field(description="List of target statement context")
    validation_notes: str = Field(description="Validation notes for the context creation")


class ValidationOutput(BaseModel):
    """Model for validation AI output."""
    is_correct: bool = Field(description="Whether the conversion is correct")
    explanation: str = Field(description="Explanation of the validation result")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")


class SyntaxValidationOutput(BaseModel):
    """Model for syntax validation AI output."""
    is_syntactically_equivalent: bool = Field(description="Whether statements are syntactically equivalent")
    explanation: str = Field(description="Explanation of the validation result")
    equivalence_score: float = Field(description="Equivalence score between 0.0 and 1.0")
    translation_quality: str = Field(description="Quality of translation: excellent, good, fair, poor")
    syntax_differences: List[str] = Field(description="List of identified syntax differences")


class StatementMappingItem(BaseModel):
    """Model for a single statement mapping."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    confidence: float = Field(description="Confidence score between 0.0 and 1.0")
    reason: str = Field(description="Reason for this mapping")


class MappingOutput(BaseModel):
    """Model for mapping AI output."""
    mappings: List[StatementMappingItem] = Field(description="List of statement mappings")


class WrongMappingItem(BaseModel):
    """Model for a wrong mapping identification."""
    source_line: int = Field(description="Source statement line number")
    target_line: int = Field(description="Target statement line number")
    reason: str = Field(description="Reason why this mapping is wrong")


class WrongMappingOutput(BaseModel):
    """Model for wrong mapping identification AI output."""
    wrong_mappings: List[WrongMappingItem] = Field(description="List of wrong mappings")


class Phase1IdentificationOutput(BaseModel):
    """Model for Phase 1 error statement identification."""
    source_statement_number: int = Field(description="The identified source statement number")
    confidence_score: float = Field(description="Confidence score between 0.0 and 1.0")
    reasoning: str = Field(description="Reasoning for the identification")


class SourceStatementItem(BaseModel):
    """Model for a source statement mapping item."""
    target_statement_number: int = Field(description="Target statement number")
    source_statement_number: int = Field(description="Source statement number")
    statement_type: str = Field(description="Type: before_error, error_statement, or after_error")


class Phase2MappingOutput(BaseModel):
    """Model for Phase 2 sequential mapping output."""
    source_statements: List[SourceStatementItem] = Field(description="List of source statement mappings")
    validation_notes: str = Field(description="Validation notes for the mapping")


class StatementMapping(BaseModel):
    """Mapping between source and target statements."""
    source_statement: str = Field(description="The source statement")
    source_line_number: int = Field(description="The line number of the source statement (1-based)")
    target_statement: str = Field(description="The target statement")
    target_line_number: int = Field(description="The line number of the target statement (1-based)")
    status: Literal["Matched", "Source Only", "Target Only", "Target Only (Duplicate)"] = Field(
        description="The status of the mapping: Matched, Source Only, Target Only, or Target Only (Duplicate)"
    )


class ErrorContext(BaseModel):
    """Context around an error statement."""
    before_statement: str = Field(description="Statement before the error statement")
    before_statement_number: int = Field(description="Line number of the statement before the error")
    error_statement: str = Field(description="The statement with the error")
    error_statement_number: int = Field(description="Line number of the error statement")
    after_statement: str = Field(description="Statement after the error statement")
    after_statement_number: int = Field(description="Line number of the statement after the error")


class WorkflowState(BaseModel):
    """Workflow state for code processing pipeline."""
    source_code: str = Field(
        description="Original Source code to be processed"
    )
    target_code: str = Field(
        description="Original Target code to be processed"
    )
    deployment_error: Optional[str] = Field(
        default=None,
        description="Error message from deployment"
    )
    source_statements: Optional[List[str]] = Field(
        default=None,
        description="Source SQL statements after splitting"
    )
    target_statements: Optional[List[str]] = Field(
        default=None,
        description="Target SQL statements after splitting"
    )
    target_error_context: Optional[ErrorContext] = Field(
        default=None,
        description="Context around the error statement"
    )
    source_context: Optional[ErrorContext] = Field(
        default=None,
        description="Corresponding source context for the error"
    )
    corrected_target_statements: Optional[List[str]] = Field(
        default=None,
        description="Target statements after correction"
    )
    updated_target_code: Optional[str] = Field(
        default=None,
        description="Updated target code after correction"
    )
    deployment_successful: Optional[bool] = Field(
        default=None,
        description="Whether the deployment was successful"
    )
    error_message: Optional[str] = Field(
        default=None,
        description="Error message from deployment if it failed"
    )
    validation_successful: Optional[bool] = Field(
        default=None,
        description="Whether the error identification validation was successful"
    )
    validation_attempts: Optional[int] = Field(
        default=0,
        description="Number of validation attempts performed"
    )
    source_mapping_successful: Optional[bool] = Field(
        default=None,
        description="Whether the source mapping validation was successful"
    )
    source_mapping_attempts: Optional[int] = Field(
        default=0,
        description="Number of source mapping validation attempts performed"
    )
    conversion_successful: Optional[bool] = Field(
        default=None,
        description="Whether the conversion validation was successful"
    )
    conversion_attempts: Optional[int] = Field(
        default=0,
        description="Number of conversion validation attempts performed"
    )

    iteration_count: Optional[int] = Field(
        default=1,
        description="Current iteration count for the workflow"
    )

    mappings: Optional[List[StatementMapping]] = Field(
        default=None,
        description="Mappings between source and target statements"
    )

    # Validation feedback fields for AI learning
    error_identification_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from error identification validation failure"
    )
    source_mapping_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from source mapping validation failure"
    )
    conversion_feedback: Optional[str] = Field(
        default=None,
        description="Feedback from conversion validation failure"
    )

    # AI corrections fields for new workflow
    ai_corrections: Optional[List[CorrectedStatement]] = Field(
        default=None,
        description="AI-generated corrections from Convert_TargetStatement node"
    )
    conversion_explanation: Optional[str] = Field(
        default=None,
        description="Explanation from AI conversion process"
    )
    original_target_statements: Optional[List[str]] = Field(
        default=None,
        description="Original target statements before applying corrections"
    )

