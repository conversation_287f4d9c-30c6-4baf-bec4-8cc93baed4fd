import uuid
from typing import Dict, Any
from langgraph.graph import State<PERSON>raph, END, START
from langgraph.checkpoint.memory import MemorySaver
from state import WorkflowState
from nodes.conversion_nodes import UniversalCodeMigrationNodes
from langchain_core.runnables.graph import MermaidDrawMethod



class GraphBuilder:
    """
    Graph builder for Oracle to PostgreSQL database migration workflow.

    This class constructs and manages the complete workflow graph including all nodes,
    edges, and conditional logic for the database migration process. It handles
    error identification, source mapping, statement conversion, and deployment validation.
    """

    def __init__(self, llm):
        """
        Initialize the graph builder with a language model.

        Args:
            llm: Language model instance for AI-driven workflow operations
        """
        self.llm = llm
        self.builder = StateGraph(WorkflowState)
        self.memory = MemorySaver()

    def build_graph(self):
        """
        Configure the complete workflow graph by adding all nodes and edges.

        Creates a comprehensive workflow that handles the entire Oracle to PostgreSQL
        migration process including validation loops and error recovery mechanisms.

        Returns:
            Configured StateGraph builder ready for compilation
        """
        # Create a new builder to avoid any cached nodes
        self.builder = StateGraph(WorkflowState)
        self.conversion_nodes = UniversalCodeMigrationNodes(llm=self.llm)

        # Add nodes for Oracle to PostgreSQL conversion workflow
        self.builder.add_node("splitStatments", self.conversion_nodes.splitStatments)
        self.builder.add_node("AnalyzeError_identifyTargetStatements", self.conversion_nodes.AnalyzeError_identifyTargetStatements)
        self.builder.add_node("validate_error_identification", self.conversion_nodes.validate_error_identification)
        self.builder.add_node("mapSource_withTargetStatements", self.conversion_nodes.mapSource_withTargetStatements)
        self.builder.add_node("validate_source_mapping", self.conversion_nodes.validate_source_mapping)
        self.builder.add_node("Convert_TargetStatement", self.conversion_nodes.Convert_TargetStatement)
        self.builder.add_node("validate_conversion", self.conversion_nodes.validate_conversion)
        self.builder.add_node("replaceTargetStatement", self.conversion_nodes.replaceTargetStatement)
        self.builder.add_node("targetcode_deployment", self.conversion_nodes.targetcode_deployment)
        self.builder.add_node("deployment_status", self.conversion_nodes.deployment_status)

        # Define workflow: split → identify error → validate error → map source → validate mapping → convert → replace → deploy → check status → (end or back to analyze)
        self.builder.add_edge(START, "splitStatments")
        self.builder.add_edge("splitStatments", "AnalyzeError_identifyTargetStatements")
        self.builder.add_edge("AnalyzeError_identifyTargetStatements", "validate_error_identification")

        # Add conditional edges from error validation
        self.builder.add_conditional_edges(
            "validate_error_identification",
            self.should_continue_validation,
            {
                "continue": "AnalyzeError_identifyTargetStatements",  # If validation failed, go back to analyze error
                "proceed": "mapSource_withTargetStatements"  # If validation succeeded, proceed to next step
            }
        )
        self.builder.add_edge("mapSource_withTargetStatements", "validate_source_mapping")

        # Add conditional edges from source mapping validation
        self.builder.add_conditional_edges(
            "validate_source_mapping",
            self.should_continue_source_mapping,
            {
                "continue": "mapSource_withTargetStatements",  # If validation failed, go back to map source
                "proceed": "Convert_TargetStatement"  # If validation succeeded, proceed to next step
            }
        )
        self.builder.add_edge("Convert_TargetStatement", "validate_conversion")

        # Add conditional edges from conversion validation
        self.builder.add_conditional_edges(
            "validate_conversion",
            self.should_continue_conversion,
            {
                "continue": "Convert_TargetStatement",  # If validation failed, go back to convert
                "proceed": 'replaceTargetStatement'  # If validation succeeded, end the workflow
            }
        )
        self.builder.add_edge("replaceTargetStatement", "targetcode_deployment")

        self.builder.add_edge("targetcode_deployment", "deployment_status")
        # self.builder.add_edge("deployment_status", END)
        # Add conditional edges from deployment_status
        self.builder.add_conditional_edges(
            "deployment_status",
            self.should_continue_or_end,
            {
                "continue": "splitStatments",  # If deployment failed, go back to split statements first
                "end": END  # If deployment succeeded, end the workflow
            }
        )
        return self.builder



    def setup_graph(self):
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            input_data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}, "recursion_limit": 200}
        return self.graph.invoke(data, config=thread)

    def should_continue_validation(self, state: WorkflowState) -> str:
        """
        Determine if error identification validation should continue or proceed to next step.

        Evaluates the validation results and attempt count to decide whether to retry
        error identification or proceed to source mapping phase.

        Args:
            state: Current workflow state containing validation results

        Returns:
            "continue" to retry error identification, "proceed" to move to source mapping
        """
        validation_successful = getattr(state, 'validation_successful', False)
        validation_attempts = getattr(state, 'validation_attempts', 0)

        if validation_successful:
            print(f"✅ Validation successful after {validation_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Validation failed (attempt {validation_attempts}) - trying again")
            return "continue"

    def should_continue_source_mapping(self, state: WorkflowState) -> str:
        """Determine if we should continue source mapping validation or proceed based on validation results."""
        source_mapping_successful = getattr(state, 'source_mapping_successful', False)
        source_mapping_attempts = getattr(state, 'source_mapping_attempts', 0)

        if source_mapping_successful:
            print(f"✅ Source mapping validation successful after {source_mapping_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Source mapping validation failed (attempt {source_mapping_attempts}) - trying again")
            return "continue"

    def should_continue_conversion(self, state: WorkflowState) -> str:
        """Determine if we should continue conversion validation or proceed based on validation results."""
        conversion_successful = getattr(state, 'conversion_successful', False)
        conversion_attempts = getattr(state, 'conversion_attempts', 0)

        if conversion_successful:
            print(f"✅ Conversion validation successful after {conversion_attempts} attempts - proceeding to next step")
            return "proceed"
        else:
            print(f"❌ Conversion validation failed (attempt {conversion_attempts}) - trying again")
            return "continue"

    def should_continue_or_end(self, state: WorkflowState) -> str:
        """Determine if we should continue the workflow or end based on deployment status."""
        deployment_successful = getattr(state, 'deployment_successful', False)
        error_message = getattr(state, 'error_message', None)
        updated_target_code = getattr(state, 'updated_target_code', None)

        if deployment_successful:
            print("✅ Deployment successful - ending workflow")
            return "end"
        else:
            print("❌ Deployment failed - going back to split statements")
            print(f"🔄 New error message: {error_message}")

            if updated_target_code:
                print(f"🔄 Using updated target code for next iteration")
            else:
                print(f"⚠️ No updated target code found")

            return "continue"

    def save_graph_image(self, graph):
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
                )

            # # Generate the PNG image using Pyppeteer (local browser rendering)
            # img_data = graph.get_graph().draw_mermaid_png(
            #     draw_method=MermaidDrawMethod.PYPPETEER,
            #     max_retries=5,
            #     retry_delay=2.0
            # )

            # Save the image to a file
            graph_path = "workflow_graph.png"
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate graph image: {str(e)}")
            print("Continuing execution without graph visualization...")