"""
Prompts for src_tgt validation in database conversion.
"""
from typing import Dict

def create_src_tgt_validation_prompt(source_context: Dict, target_error_context: Dict, corrected_statement: str, target_statements: list = None) -> str:
    """Create a src_tgt validation prompt for conversion validation."""
    return f"""You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to validate if the corrected target PostgreSQL statement properly resolves the error and maintains functional equivalence using error-driven validation approach.

SOURCE STATEMENT:
{source_context.error_statement}

ORIGINAL TARGET STATEMENT (with error):
{target_error_context.error_statement}

CORRECTED TARGET STATEMENT:
{corrected_statement}

ADDITIONAL CONTEXT:
{f"Complete target code context available for analysis - examine the full structure to understand function signatures, parameter types, and return patterns" if target_statements else "Limited context available"}

ERROR-DRIVEN VALIDATION APPROACH:
Follow this systematic validation methodology:

STEP 1: ERROR RESOLUTION ANALYSIS (Primary)
- Does the corrected statement directly address the specific error that was reported?
- Are the syntax violations mentioned in the original error now resolved?
- Would deploying this corrected statement eliminate the reported deployment failure?
- **CRITICAL**: Is the corrected statement EXECUTABLE PostgreSQL code (not just comments)?
- **REJECT**: Any solution that only contains comments, explanations, or TODO notes
- **REQUIRE**: Working PostgreSQL code that performs the intended business operation

STEP 2: TARGET DATABASE COMPLIANCE (Secondary)
- Does the corrected statement follow correct PostgreSQL syntax rules?
- If this is within a function, does it respect the function signature requirements?
- Are parameter types (IN, OUT, INOUT) handled correctly according to PostgreSQL patterns?
- Is this the standard way to implement this construct in PostgreSQL?

STEP 3: BUSINESS LOGIC PRESERVATION (Tertiary)
- Does the corrected statement maintain the same business purpose as the source?
- Is the data flow and functionality equivalent to the source?
- Are the intended outcomes preserved?
- For target database-specific statements: Does the correction use appropriate target database expertise?

TASK:
1. Analyze if the corrected statement directly resolves the reported error
2. Validate target database syntax compliance and database-specific requirements
3. For statements with source equivalents: Confirm that business logic and functionality are preserved
4. For target database-specific statements: Validate that appropriate target database expertise was applied
5. Provide overall validation decision based on error resolution effectiveness

VALIDATION CRITERIA (Priority Order):

1. **ERROR RESOLUTION EFFECTIVENESS (Primary)**
   - Does the corrected statement directly fix the specific error that was reported?
   - Are all syntax violations mentioned in the error message now resolved?
   - Would this correction eliminate the deployment failure?
   - Is the error-causing element properly addressed?
   - **EXECUTABLE CODE REQUIREMENT**: Is the corrected statement actual PostgreSQL code that executes business logic?
   - **REJECT COMMENTS**: Does the solution contain only comments, explanations, or documentation?
   - **FUNCTIONAL IMPLEMENTATION**: Does the corrected statement perform the intended operation?

2. **TARGET DATABASE COMPLIANCE (Secondary)**
   - Does the corrected statement follow correct PostgreSQL syntax rules?
   - Are database-specific patterns correctly implemented?
   - For functions: Are parameter types and return patterns handled correctly?
   - Is this the proper PostgreSQL way to implement this construct?

3. **BUSINESS LOGIC PRESERVATION (Tertiary)**
   - Does the corrected statement perform the same operation as the source for ANY operation type including:
     * Data Manipulation: SELECT, INSERT, UPDATE, DELETE, MERGE, UPSERT operations
     * Data Definition: CREATE, ALTER, DROP, TRUNCATE, RENAME operations
     * Transaction Control: COMMIT, ROLLBACK, SAVEPOINT, BEGIN operations
     * Procedural Operations: DECLARE, SET, CALL, EXECUTE, RETURN operations
     * Control Flow: IF/ELSE, CASE/WHEN, LOOP, WHILE, FOR, EXCEPTION handling
     * Cursor Operations: OPEN, FETCH, CLOSE cursor operations
     * Aggregate Operations: GROUP BY, HAVING, window functions, analytical functions
     * Join Operations: INNER/OUTER/CROSS joins, subqueries, CTEs
     * Index Operations: CREATE/DROP INDEX, hints, optimization directives
     * Security Operations: GRANT, REVOKE, user/role management
     * System Operations: sequence generation, trigger definitions, view creation
   - Are the business logic and data transformations equivalent?
   - Are the expected results and functional outcomes the same?

2. **ERROR RESOLUTION**
   - Does the corrected statement fix the original error?
   - Are syntax issues properly resolved for the target database dialect?
   - Are function name mismatches corrected (e.g., SYSDATE → current_timestamp)?
   - Are data type incompatibilities resolved (NUMBER → NUMERIC, VARCHAR2 → VARCHAR)?
   - Are string function differences addressed (SUBSTR → SUBSTRING, CONCAT → ||)?
   - Are XML/JSON processing functions properly converted?
   - Are sequence generation methods correctly translated (NEXTVAL → nextval())?
   - Is the statement now valid and executable for the target database?

3. **FUNCTIONAL PRESERVATION**
   - Is the original functionality and business logic preserved?
   - Are edge cases and error conditions handled correctly?
   - Is data integrity and consistency maintained?
   - Are performance characteristics preserved where possible?
   - Are procedural logic and control flow structures maintained?
   - Is exception handling appropriately converted?

4. **TRANSLATION QUALITY**
   - Are database-specific functions properly translated across dialects?
   - Are data types handled correctly for the target database?
   - Are mathematical and statistical functions appropriately converted?
   - Are cursor operations and lifecycle management correctly translated?
   - Are transaction control statements appropriately converted?
   - Is the syntax appropriate and optimal for the target database?
   - Are schema references and object naming conventions properly handled?

IMPORTANT GUIDELINES:
- PRIMARY FOCUS: Ensure the original error is completely resolved
- SECONDARY FOCUS: Validate PostgreSQL syntax compliance and best practices
- TERTIARY FOCUS: Confirm functional equivalence between source and corrected target
- Use error message as primary guide for validation success
- Consider database-specific requirements and patterns

OUTPUT FORMAT (JSON):
{{
  "is_correct": true/false,
  "confidence": <float between 0.0 and 1.0>,
  "explanation": "<comprehensive error-driven validation analysis including: 1) Error resolution assessment - how the corrected statement addresses the specific error, 2) PostgreSQL syntax compliance - validation of target database requirements, 3) Business logic preservation - confirmation that functionality is maintained, 4) Specific comparison between source and corrected statements, 5) Step-by-step verification of the correction effectiveness, 6) Assessment of any remaining issues or concerns, 7) Overall reasoning for the validation decision based on error resolution priority>",
  "error_resolution": {{
    "original_error_fixed": true/false,
    "syntax_valid": true/false,
    "functionality_preserved": true/false
  }},
  "src_tgt_assessment": {{
    "operation_equivalence": true/false,
    "data_transformation_equivalent": true/false,
    "business_logic_preserved": true/false
  }}
}}"""
