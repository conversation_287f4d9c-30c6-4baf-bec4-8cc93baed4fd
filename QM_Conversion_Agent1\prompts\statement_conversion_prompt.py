"""
Prompts for statement conversion in database conversion.
"""
from typing import Dict

# Reusable prompt components for efficiency
NAMING_PRESERVATION_RULES = """🚨 CRITICAL NAMING PRESERVATION RULE 🚨
PRESERVE ALL ORIGINAL NAMES EXACTLY AS THEY APPEAR - NO MODIFICATIONS ALLOWED
- Keep ALL function/procedure/label names EXACTLY as found in the source code
- Do NOT add any prefixes, suffixes, or modify the original naming pattern
- Do NOT convert to different naming conventions (snake_case, camelCase, etc.)
- Use the EXACT same identifiers throughout the entire conversion process"""

NO_TEMPLATES_RULE = """🚨 NO TEMPLATES OR <PERSON><PERSON><PERSON>H<PERSON>DERS ALLOWED 🚨
PROVIDE ONLY EXECUTABLE CODE - NO COMMENTS, TEMPLATES, OR PLACEHOLDERS
- Do NOT use comments as solutions or implementations
- Do NOT provide template code with placeholder values
- Do NOT add explanatory comments in place of actual code
- ALWAYS provide complete working database operations
- Use REAL table names, column names, and conditions from the actual code"""

COMPREHENSIVE_FIX_RULE = """🚨 COMPREHENSIVE PATTERN FIXING 🚨
- **CRITICAL: Scan the ENTIRE statement for ALL instances of the same error pattern**
- **Fix ALL occurrences of the same issue within the statement, not just the first one**
- **MANDATORY COMPLETE SCAN**: Count how many times the error pattern appears and fix EVERY single instance
- **NESTED STATEMENT HANDLING**: If the error statement contains multiple INSERT/SELECT/UPDATE statements, scan and fix the pattern in ALL nested statements"""

def create_statement_conversion_prompt(source_context: Dict, target_error_context: Dict, error_message: str, target_statements: list = None, previous_feedback: str = None) -> str:
    """
    Creates a prompt for converting the error statement.

    This function creates a prompt that instructs the LLM to convert the error statement
    from the target context to fix the error, using the source context as a reference.

    Args:
        source_context: Dictionary containing the source context (before, error, after statements)
        target_error_context: Dictionary containing the target error context
        error_message: The error message from deployment

    Returns:
        A formatted prompt string for the LLM
    """
    # Check if this is a target database-specific scenario (no source mapping)
    is_target_specific = (source_context.error_statement_number == 0 or
                         not source_context.error_statement or
                         source_context.error_statement.strip() == "")

    # Add feedback section if provided
    feedback_section = ""
    if previous_feedback:
        feedback_section = f"""
PREVIOUS CONVERSION FEEDBACK:
The previous conversion attempt was rejected with this feedback:
{previous_feedback}

Please address these specific conversion issues:
- Fix the exact problems mentioned in the feedback
- Ensure proper target database syntax and data types
- {"Apply target database expertise directly" if is_target_specific else "Verify functional equivalence with the source"}
- Pay attention to target database-specific requirements
- Provide more detailed analysis of the conversion changes

"""

    if is_target_specific:
        # Target database-specific conversion prompt (no source dependency)
        return f"""{NAMING_PRESERVATION_RULES}

{NO_TEMPLATES_RULE}

{COMPREHENSIVE_FIX_RULE}

You are a Target Database Expert with deep expertise in target database systems. Your task is to fix the error in the target database statement using target database expertise directly.

TARGET DATABASE-SPECIFIC CONVERSION DETECTED:
This statement appears to be target database-specific with no source equivalent. Apply target database expertise directly to resolve the syntax error.

{feedback_section}

ERROR MESSAGE:
{error_message}

TARGET ERROR CONTEXT (Target Database with error):
Before Error (#{target_error_context.before_statement_number}): {target_error_context.before_statement}
Error Statement (#{target_error_context.error_statement_number}): {target_error_context.error_statement}
After Error (#{target_error_context.after_statement_number}): {target_error_context.after_statement}

ANALYSIS STEPS:
1. **ERROR ANALYSIS**: What specific rule/syntax is violated? What needs to be changed?
2. **CONTEXT ANALYSIS**: Examine complete target code structure for function signatures, types, patterns
3. **TARGET DATABASE EXPERTISE**: Apply deep database knowledge to fix using native patterns
4. **COMPREHENSIVE FIX**: Fix ALL instances of the same error pattern throughout the statement

TASK: Use target database expertise to fix ANY type of issue. Handle syntax fixes, logic corrections, data type issues, function signature problems, or any database-specific challenges.

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original target statement>",
      "corrected_statement": "<corrected target statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<detailed description: 1) What was wrong, 2) Transformations applied, 3) Why necessary, 4) How it resolves error, 5) Database expertise applied>"
    }}
  ],
  "explanation": "<comprehensive analysis: 1) Error interpretation, 2) Database syntax requirements, 3) Expertise applied, 4) Fix rationale, 5) Best practices used, 6) Validation>"
}}

IMPORTANT: Apply target database expertise directly. Focus on target database syntax and best practices. Return ALL statements in the corrected_statements array."""

    else:
        # Standard conversion prompt (with source reference)
        return f"""{NAMING_PRESERVATION_RULES}

{NO_TEMPLATES_RULE}

{COMPREHENSIVE_FIX_RULE}

You are an Oracle to PostgreSQL Database Migration Expert with deep expertise in both Oracle and PostgreSQL database systems. Your task is to fix the error in the target PostgreSQL statement by converting it correctly from the Oracle source.

{feedback_section}

ERROR MESSAGE:
{error_message}

SOURCE CONTEXT (Oracle):
Before Error (#{source_context.before_statement_number}): {source_context.before_statement}
Error Statement (#{source_context.error_statement_number}): {source_context.error_statement}
After Error (#{source_context.after_statement_number}): {source_context.after_statement}

TARGET CONTEXT (PostgreSQL with error):
Before Error (#{target_error_context.before_statement_number}): {target_error_context.before_statement}
Error Statement (#{target_error_context.error_statement_number}): {target_error_context.error_statement}
After Error (#{target_error_context.after_statement_number}): {target_error_context.after_statement}

ANALYSIS STEPS:
1. **ERROR ANALYSIS**: What specific rule/syntax is violated? What needs to be changed?
2. **CONTEXT ANALYSIS**: Examine database object type, function signatures, parameter types, return patterns
3. **SOURCE-TARGET MAPPING**: Use Oracle source as reference for business logic and functionality
4. **POSTGRESQL CONVERSION**: Apply PostgreSQL syntax rules and best practices
5. **COMPREHENSIVE FIX**: Fix ALL instances of the same error pattern throughout the statement
6. **UNSUPPORTED FEATURE HANDLING**: For any unsupported Oracle features, find PostgreSQL equivalents that achieve the same business outcome

ORACLE TO POSTGRESQL CONVERSION EXPERTISE:
- Oracle procedures vs PostgreSQL functions handle return values differently
- Oracle OUT parameters vs PostgreSQL OUT parameters may use different syntax patterns
- Oracle data types vs PostgreSQL data types require different casting approaches
- Focus on PostgreSQL-native patterns rather than direct Oracle translations

UNSUPPORTED FEATURE CONVERSION STRATEGY:
For any unsupported Oracle features, apply this conversion approach:
1. **Identify**: What specific syntax/function/construct is causing the error?
2. **Understand**: What business purpose does this feature serve?
3. **Find Equivalents**: What PostgreSQL features can achieve the same outcome?
4. **Implement**: Create working PostgreSQL solution that preserves functionality

CONVERSION PATTERNS:
- **Functions**: Replace with PostgreSQL equivalents or custom implementations
- **Syntax**: Rewrite using PostgreSQL-compatible patterns
- **Data Types**: Convert to PostgreSQL types with proper casting
- **Control Structures**: Replace with PostgreSQL control flow constructs
- **Operators**: Use PostgreSQL-compatible operators or function equivalents

TASK:
1. **Analyze error message** as primary guide for what needs fixing
2. **Use Oracle source** as reference for business logic and functionality
3. **Identify unsupported features** causing the error (syntax, functions, operators, etc.)
4. **Apply PostgreSQL conversion** to find alternatives for ANY unsupported feature
5. **🚨 PRESERVE ORIGINAL NAMING**: Keep all function/procedure/label names EXACTLY as found
6. **🚨 COMPREHENSIVE FIX**: Scan ENTIRE error statement for ALL instances of same error pattern and fix ALL occurrences
7. **CRITICAL: Fix ONLY the ERROR STATEMENT** - before/after are for context only
8. **Validate** that business logic and functionality are preserved

CONVERSION GUIDELINES:
- **ALWAYS provide complete EXECUTABLE implementations** that maintain original functionality
- **NEVER use comments as solutions** - provide working PostgreSQL code
- **ANALYZE business purpose** of unsupported features before converting
- **USE PostgreSQL best practices** to achieve same business outcomes
- **IMPLEMENT actual operations** using real table/column names from the code

OUTPUT FORMAT (JSON):
{{
  "corrected_statements": [
    {{
      "statement_number": <integer>,
      "original_statement": "<original target statement>",
      "corrected_statement": "<corrected target statement>",
      "statement_type": "before_error|error_statement|after_error",
      "changes_made": "<description: 1) What was wrong, 2) Transformations applied, 3) Why necessary, 4) How it resolves error>"
    }}
  ],
  "explanation": "<analysis: 1) Error interpretation, 2) Unsupported feature analysis, 3) PostgreSQL alternatives, 4) Implementation strategy, 5) Business logic preservation>"
}}

IMPORTANT:
- Return ALL three statements (before, error, after) in corrected_statements array
- Use EXACT statement numbers: Before={target_error_context.before_statement_number}, Error={target_error_context.error_statement_number}, After={target_error_context.after_statement_number}
- **PRIMARY FOCUS: Fix ONLY the deployment error in the error statement**
- **CONTEXT ONLY: Before/after statements are for context - keep them exactly as original_statement**
- **For error statement: Provide complete alternative implementation that fixes the error**"""
